import pytest
from unittest.mock import Mock, MagicMock
from llama_tune.cli import tune
import sys

@pytest.fixture
def mock_benchmarking_engine(mocker):
    mock_engine = mocker.patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine', autospec=True).return_value
    mock_engine.run_benchmark.return_value = Mock(
        system_profile=Mock(cpu_cores=4, total_ram_gb=8.0, gpus=[], numa_detected=False, blas_backend="None"),
        model_profile=Mock(file_path="/path/to/model.gguf", architecture="llama", layer_count=32, quantization_type="Q4_K_M"),
        best_benchmark_result=Mock(n_gpu_layers=16, prompt_speed_tps=100.0, generation_speed_tps=50.0, batch_size=1, parallel_level=1),
        generated_command="mock command",
        notes=["mock note"],
        ctx_size=2048,
        sampling_parameters={},
        use_case="default",
        max_vram_gb=None
    )
    return mock_engine

def test_progress_callback_updates_progress_bar(mock_benchmarking_engine, mocker):
    # Patch the Progress class itself
    mock_progress_class = mocker.patch('llama_tune.cli.Progress')
    
    # Create a MagicMock that behaves as a context manager
    mock_progress_instance = MagicMock()
    mock_progress_instance.__enter__.return_value = mock_progress_instance
    mock_progress_instance.__exit__.return_value = None

    # Set the return_value of the patched Progress class to our context manager mock
    mock_progress_class.return_value = mock_progress_instance

    mocker.patch('llama_tune.cli.Console')
    mocker.patch('typer.echo')
    mocker.patch('sys.exit')
    # Correctly patch the functions as they are imported into cli.py with aliases
    mocker.patch('llama_tune.cli.analyzer_get_model_profile', return_value=Mock(
        file_path="/path/to/model.gguf", architecture="llama", layer_count=32, quantization_type="Q4_K_M"
    ))
    mocker.patch('llama_tune.cli.analyzer_get_system_profile', return_value=Mock(
        cpu_cores=4, total_ram_gb=8.0, gpus=[], numa_detected=False, blas_backend="None"
    ))
    mocker.patch('llama_tune.cli.analyzer_run_feasibility_check', return_value=None)

    # Also patch the functions in the benchmarking engine
    mocker.patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=Mock(
        file_path="/path/to/model.gguf", architecture="llama", layer_count=32, quantization_type="Q4_K_M"
    ))
    mocker.patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=Mock(
        cpu_cores=4, total_ram_gb=8.0, gpus=[], numa_detected=False, blas_backend="None"
    ))

    # Mock the add_task method on the instance
    mock_overall_task = mock_progress_instance.add_task.return_value

    # Mock the benchmarking engine class to simulate progress callbacks
    def mock_run_benchmark(model_path, ctx_size, use_case, num_runs, progress_callback, max_vram_gb, dry_run=False):
        # Simulate some progress callbacks
        if progress_callback:
            progress_callback("Phase 1/2 GPU Offload", "Testing --n-gpu-layers 1/32", 1, 32, 10.50)
            progress_callback("Phase 1/2 GPU Offload", "Testing --n-gpu-layers 2/32", 2, 32, 12.30)
            progress_callback("Phase 2/2 Throughput", "Testing --parallel 4", 1, 6, 25.70)

        # Return a mock OptimalConfiguration with proper structure
        from llama_tune.core.data_models import OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult
        return OptimalConfiguration(
            system_profile=SystemProfile(
                cpu_cores=4,
                total_ram_gb=8.0,
                gpus=[],
                numa_detected=False,
                blas_backend="None"
            ),
            model_profile=ModelProfile(
                file_path="/path/to/model.gguf",
                architecture="llama",
                layer_count=32,
                quantization_type="Q4_K_M"
            ),
            best_benchmark_result=BenchmarkResult(
                n_gpu_layers=16,
                prompt_speed_tps=10.50,
                generation_speed_tps=25.70,
                batch_size=512,
                parallel_level=4
            ),
            generated_command="",
            notes=["Test note"]
        ), [] # Return an empty list for all_benchmark_results

    # Create a mock benchmarking engine instance
    mock_engine_instance = Mock()
    mock_engine_instance.run_benchmark = mock_run_benchmark

    # Mock the BenchmarkingEngine class to return our mock instance
    mocker.patch('llama_tune.cli.BenchmarkingEngine', return_value=mock_engine_instance)

    # Simulate the benchmark call
    tune(
        benchmark=True,
        model_path="/path/to/model.gguf",
        ctx_size=2048,
        verbose=False,
        json_output=False,
        interactive=False,
        llama_server_url=None,
        max_vram_gb=None
    )

    # Assert that Progress class was instantiated
    mock_progress_class.assert_called_once()
    # Assert that the context manager methods were called
    mock_progress_instance.__enter__.assert_called_once()
    mock_progress_instance.__exit__.assert_called_once()

    # Assert that add_task was called for the overall progress
    mock_progress_instance.add_task.assert_called_once_with(
        "[green]Overall Benchmark Progress", total=100
    )

    # Assert that update was called on the overall task for each simulated call
    # The actual calls include the task as the first argument
    expected_calls = [
        mocker.call(mock_overall_task, description="[green]Phase 1/2 GPU Offload: [cyan]Testing --n-gpu-layers 1/32 - 10.50 t/s", completed=(1/32)*100),
        mocker.call(mock_overall_task, description="[green]Phase 1/2 GPU Offload: [cyan]Testing --n-gpu-layers 2/32 - 12.30 t/s", completed=(2/32)*100),
        mocker.call(mock_overall_task, description="[green]Phase 2/2 Throughput: [cyan]Testing --parallel 4 - 25.70 t/s", completed=(1/6)*100),
    ]
    mock_progress_instance.update.assert_has_calls(expected_calls, any_order=False)