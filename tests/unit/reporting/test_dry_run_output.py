import pytest
from llama_tune.reporting.output_generator import generate_dry_run_plan
from llama_tune.core.data_models import BenchmarkPlan, BenchmarkPhase, SystemProfile, ModelProfile, GpuInfo


@pytest.fixture
def sample_system_profile():
    """Sample system profile for testing."""
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[
            GpuInfo(model_name="NVIDIA RTX 4080", vram_gb=16.0),
            GpuInfo(model_name="NVIDIA RTX 3070", vram_gb=8.0)
        ],
        numa_detected=False,
        blas_backend="CUDA"
    )


@pytest.fixture
def sample_model_profile():
    """Sample model profile for testing."""
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_0"
    )


@pytest.fixture
def sample_benchmark_plan_default(sample_system_profile, sample_model_profile):
    """Sample benchmark plan for default use case."""
    phases = [
        BenchmarkPhase(
            name="Phase 1: GPU Offload Optimization",
            description="Will test up to 32 GPU layers using binary search to find optimal offloading",
            estimated_steps=18,
            estimated_duration_minutes=9.0
        )
    ]
    
    return BenchmarkPlan(
        system_profile=sample_system_profile,
        model_profile=sample_model_profile,
        ctx_size=2048,
        use_case="default",
        max_vram_gb=None,
        phases=phases,
        total_estimated_steps=18,
        total_estimated_duration_minutes=9.0,
        notes=[
            "This is a dry-run preview. No actual benchmarks will be executed.",
            "Estimated total time: 9.0 minutes",
            "Model has 32 layers to potentially offload to GPU",
            "Available GPUs: NVIDIA RTX 4080, NVIDIA RTX 3070"
        ]
    )


@pytest.fixture
def sample_benchmark_plan_multi_user(sample_system_profile, sample_model_profile):
    """Sample benchmark plan for multi-user-server use case."""
    phases = [
        BenchmarkPhase(
            name="Phase 1: GPU Offload Optimization",
            description="Will test up to 32 GPU layers using binary search to find optimal offloading",
            estimated_steps=18,
            estimated_duration_minutes=9.0
        ),
        BenchmarkPhase(
            name="Phase 2: Throughput Optimization",
            description="Will test 8 parallel configurations and 5 batching configurations",
            estimated_steps=39,
            estimated_duration_minutes=39.0
        )
    ]
    
    return BenchmarkPlan(
        system_profile=sample_system_profile,
        model_profile=sample_model_profile,
        ctx_size=4096,
        use_case="multi-user-server",
        max_vram_gb=12.0,
        phases=phases,
        total_estimated_steps=57,
        total_estimated_duration_minutes=48.0,
        notes=[
            "This is a dry-run preview. No actual benchmarks will be executed.",
            "Estimated total time: 48.0 minutes",
            "Model has 32 layers to potentially offload to GPU",
            "VRAM budget limited to 12.0 GB",
            "Available GPUs: NVIDIA RTX 4080, NVIDIA RTX 3070"
        ]
    )


def test_generate_dry_run_plan_default_use_case(sample_benchmark_plan_default):
    """Test dry-run plan generation for default use case."""
    output = generate_dry_run_plan(sample_benchmark_plan_default)
    
    # Verify header
    assert "Benchmark Plan (Dry Run)" in output
    assert "=" * 25 in output
    
    # Verify model information
    assert "/path/to/model.gguf" in output
    assert "llama" in output
    assert "32" in output  # layer count
    assert "Q4_0" in output
    assert "2048" in output  # context size
    assert "default" in output  # use case
    
    # Verify GPU information
    assert "Available GPUs:" in output
    assert "NVIDIA RTX 4080" in output
    assert "NVIDIA RTX 3070" in output
    assert "16.0 GB VRAM" in output
    assert "8.0 GB VRAM" in output
    
    # Verify phase information
    assert "Phase 1: GPU Offload Optimization" in output
    assert "Will test up to 32 GPU layers" in output
    assert "Estimated Steps: 18" in output
    assert "Estimated Duration: 9.0 minutes" in output
    
    # Verify summary
    assert "Total Estimated Steps: 18" in output
    assert "Total Estimated Duration: 9.0 minutes" in output
    
    # Verify notes
    assert "dry-run preview" in output
    assert "No actual benchmarks will be executed" in output
    
    # Verify instruction
    assert "To execute this benchmark plan, run the same command without --dry-run" in output


def test_generate_dry_run_plan_multi_user_server(sample_benchmark_plan_multi_user):
    """Test dry-run plan generation for multi-user-server use case."""
    output = generate_dry_run_plan(sample_benchmark_plan_multi_user)
    
    # Verify use case and context size
    assert "multi-user-server" in output
    assert "4096" in output  # context size
    
    # Verify VRAM budget
    assert "VRAM Budget: 12.0 GB" in output
    
    # Verify both phases
    assert "Phase 1: GPU Offload Optimization" in output
    assert "Phase 2: Throughput Optimization" in output
    assert "parallel configurations" in output
    assert "batching configurations" in output
    
    # Verify phase details
    assert "Estimated Steps: 18" in output  # Phase 1
    assert "Estimated Steps: 39" in output  # Phase 2
    assert "Estimated Duration: 9.0 minutes" in output  # Phase 1
    assert "Estimated Duration: 39.0 minutes" in output  # Phase 2
    
    # Verify totals
    assert "Total Estimated Steps: 57" in output
    assert "Total Estimated Duration: 48.0 minutes" in output
    
    # Verify VRAM budget in notes
    assert "VRAM budget limited to 12.0 GB" in output


def test_generate_dry_run_plan_no_gpus():
    """Test dry-run plan generation for system with no GPUs."""
    system_profile = SystemProfile(
        cpu_cores=4,
        total_ram_gb=8.0,
        gpus=[],
        numa_detected=False,
        blas_backend="OpenBLAS"
    )
    
    model_profile = ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=16,
        quantization_type="Q8_0"
    )
    
    phases = [
        BenchmarkPhase(
            name="Phase 1: GPU Offload Optimization",
            description="Will test up to 16 GPU layers using binary search to find optimal offloading",
            estimated_steps=12,
            estimated_duration_minutes=6.0
        )
    ]
    
    plan = BenchmarkPlan(
        system_profile=system_profile,
        model_profile=model_profile,
        ctx_size=1024,
        use_case="default",
        max_vram_gb=None,
        phases=phases,
        total_estimated_steps=12,
        total_estimated_duration_minutes=6.0,
        notes=[
            "This is a dry-run preview. No actual benchmarks will be executed.",
            "Estimated total time: 6.0 minutes",
            "Model has 16 layers to potentially offload to GPU",
            "No GPUs detected - will test CPU-only configurations"
        ]
    )
    
    output = generate_dry_run_plan(plan)
    
    # Verify no GPU message
    assert "GPUs: None detected (CPU-only mode)" in output
    assert "No GPUs detected - will test CPU-only configurations" in output


def test_generate_dry_run_plan_formatting():
    """Test that the dry-run plan output is properly formatted."""
    # Create minimal plan for formatting test
    system_profile = SystemProfile(
        cpu_cores=2,
        total_ram_gb=4.0,
        gpus=[],
        numa_detected=False,
        blas_backend="None"
    )
    
    model_profile = ModelProfile(
        file_path="/test/model.gguf",
        architecture="test",
        layer_count=8,
        quantization_type="Q4_K_M"
    )
    
    phases = [
        BenchmarkPhase(
            name="Test Phase",
            description="Test description",
            estimated_steps=5,
            estimated_duration_minutes=2.5
        )
    ]
    
    plan = BenchmarkPlan(
        system_profile=system_profile,
        model_profile=model_profile,
        ctx_size=512,
        use_case="test",
        max_vram_gb=None,
        phases=phases,
        total_estimated_steps=5,
        total_estimated_duration_minutes=2.5,
        notes=["Test note"]
    )
    
    output = generate_dry_run_plan(plan)
    
    # Verify structure and formatting
    lines = output.split('\n')
    assert len(lines) > 10  # Should have multiple lines
    
    # Verify sections are present
    assert any("Benchmark Plan (Dry Run)" in line for line in lines)
    assert any("Planned Benchmark Phases:" in line for line in lines)
    assert any("Summary:" in line for line in lines)
    assert any("Notes:" in line for line in lines)
    
    # Verify proper indentation for phase details
    assert any(line.startswith("  Description:") for line in lines)
    assert any(line.startswith("  Estimated Steps:") for line in lines)
    assert any(line.startswith("  Estimated Duration:") for line in lines)
