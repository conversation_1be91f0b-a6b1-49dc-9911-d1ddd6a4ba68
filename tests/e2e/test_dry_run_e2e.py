import subprocess
import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock
from llama_tune.core.data_models import SystemProfile, ModelProfile, GpuInfo
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.reporting.output_generator import generate_dry_run_plan


@pytest.fixture
def mock_system_profile():
    """Mock system profile for E2E testing."""
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[GpuInfo(model_name="NVIDIA RTX 4080", vram_gb=16.0)],
        numa_detected=False,
        blas_backend="CUDA"
    )


@pytest.fixture
def mock_model_profile():
    """Mock model profile for E2E testing."""
    return ModelProfile(
        file_path="/path/to/test_model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_0"
    )


@pytest.fixture
def temp_model_file():
    """Create a temporary file to simulate a model file."""
    with tempfile.NamedTemporaryFile(suffix=".gguf", delete=False) as f:
        f.write(b"mock model content")
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)


def test_dry_run_integration_benchmarking_engine_to_output_generator(mock_system_profile, mock_model_profile):
    """Test integration between BenchmarkingEngine and OutputGenerator for dry-run."""
    # Create benchmarking engine
    engine = BenchmarkingEngine(use_cache=False)

    with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile):

        # Run benchmark in dry-run mode
        optimal_config, all_results = engine.run_benchmark(
            model_path="/path/to/test_model.gguf",
            ctx_size=2048,
            use_case="default",
            initial_num_runs=3,
            dry_run=True
        )

        # Verify benchmark plan was created
        assert hasattr(optimal_config, 'benchmark_plan')
        assert optimal_config.benchmark_plan is not None

        # Generate dry-run output
        dry_run_output = generate_dry_run_plan(optimal_config.benchmark_plan)

        # Verify output content
        assert "Benchmark Plan (Dry Run)" in dry_run_output
        assert "Phase 1: GPU Offload Optimization" in dry_run_output
        assert "Will test up to 32 GPU layers" in dry_run_output
        assert "Estimated Steps:" in dry_run_output
        assert "Estimated Duration:" in dry_run_output
        assert "Total Estimated Steps:" in dry_run_output
        assert "Total Estimated Duration:" in dry_run_output
        assert "dry-run preview" in dry_run_output
        assert "No actual benchmarks will be executed" in dry_run_output
        assert "To execute this benchmark plan, run the same command without --dry-run" in dry_run_output

        # Verify model information is displayed
        assert "/path/to/test_model.gguf" in dry_run_output
        assert "llama" in dry_run_output
        assert "32" in dry_run_output  # layer count
        assert "Q4_0" in dry_run_output
        assert "2048" in dry_run_output  # context size
        assert "default" in dry_run_output  # use case

        # Verify GPU information
        assert "NVIDIA RTX 4080" in dry_run_output
        assert "16.0 GB VRAM" in dry_run_output


def test_dry_run_cli_integration(mock_system_profile, mock_model_profile, temp_model_file):
    """Test dry-run functionality through CLI integration."""
    from llama_tune.cli import tune

    # Patch all the analyzer functions that are called in both CLI and BenchmarkingEngine
    with patch('llama_tune.cli.analyzer_get_system_profile', return_value=mock_system_profile), \
         patch('llama_tune.cli.analyzer_get_model_profile', return_value=mock_model_profile), \
         patch('llama_tune.cli.analyzer_run_feasibility_check'), \
         patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile), \
         patch('sys.exit') as mock_exit, \
         patch('typer.echo') as mock_echo:

        # Call the tune function directly with dry-run
        tune(
            model_path=temp_model_file,
            verbose=False,
            json_output=False,
            interactive=False,
            benchmark=True,
            dry_run=True,
            ctx_size=2048,
            max_vram_gb=None,
            llama_server_url=None,
            num_runs=3,
            use_case="default",
            plot=False,
            force=False,
            no_cache=False
        )

        # Verify successful exit was called (sys.exit() without args defaults to 0)
        mock_exit.assert_called()

        # Verify dry-run output content was echoed
        assert mock_echo.called
        output_calls = [call.args[0] for call in mock_echo.call_args_list]
        output = '\n'.join(output_calls)

        # Verify key dry-run content is present
        assert "Benchmark Plan (Dry Run)" in output
        assert "Phase 1: GPU Offload Optimization" in output
        assert "dry-run preview" in output or "No actual benchmarks will be executed" in output


def test_dry_run_multi_user_server_integration(mock_system_profile, mock_model_profile):
    """Test dry-run functionality for multi-user-server use case."""
    engine = BenchmarkingEngine(use_cache=False)

    with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile):

        # Run benchmark in dry-run mode with multi-user-server use case
        optimal_config, all_results = engine.run_benchmark(
            model_path="/path/to/test_model.gguf",
            ctx_size=4096,
            use_case="multi-user-server",
            initial_num_runs=3,
            max_vram_gb=12.0,
            dry_run=True
        )

        # Verify benchmark plan contains both phases
        assert len(optimal_config.benchmark_plan.phases) == 2
        assert optimal_config.benchmark_plan.phases[0].name == "Phase 1: GPU Offload Optimization"
        assert optimal_config.benchmark_plan.phases[1].name == "Phase 2: Throughput Optimization"

        # Generate and verify output
        dry_run_output = generate_dry_run_plan(optimal_config.benchmark_plan)
        assert "Phase 1: GPU Offload Optimization" in dry_run_output
        assert "Phase 2: Throughput Optimization" in dry_run_output
        assert "parallel configurations" in dry_run_output
        assert "batching configurations" in dry_run_output
        assert "4096" in dry_run_output  # context size
        assert "multi-user-server" in dry_run_output  # use case
        assert "VRAM Budget: 12.0 GB" in dry_run_output


def test_dry_run_validation_errors(mock_system_profile, mock_model_profile):
    """Test that dry-run validation works correctly."""
    from llama_tune.cli import tune
    import pytest

    # Test that dry-run without benchmark fails
    with patch('llama_tune.cli.analyzer_get_system_profile', return_value=mock_system_profile), \
         patch('llama_tune.cli.analyzer_get_model_profile', return_value=mock_model_profile), \
         patch('llama_tune.cli.analyzer_run_feasibility_check'), \
         patch('sys.exit') as mock_exit, \
         patch('typer.echo') as mock_echo:

        tune(
            model_path="/fake/path.gguf",
            verbose=False,
            json_output=False,
            interactive=False,
            benchmark=False,  # No benchmark flag
            dry_run=True,     # But dry-run is True
            ctx_size=2048,
            max_vram_gb=None,
            llama_server_url=None,
            num_runs=3,
            use_case="default",
            plot=False,
            force=False,
            no_cache=False
        )

        # Verify the error message was echoed and exit was called with 1
        mock_exit.assert_called_with(1)
        echo_calls = [call.args[0] for call in mock_echo.call_args_list]
        assert any("--dry-run can only be used with --benchmark" in call for call in echo_calls)


def test_dry_run_no_external_processes_called(mock_system_profile, mock_model_profile):
    """Test that dry-run mode doesn't call any external llama.cpp processes."""
    engine = BenchmarkingEngine(use_cache=False)

    with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile), \
         patch('subprocess.run') as mock_subprocess, \
         patch('subprocess.Popen') as mock_popen:

        # Run benchmark in dry-run mode
        optimal_config, all_results = engine.run_benchmark(
            model_path="/path/to/test_model.gguf",
            ctx_size=2048,
            use_case="default",
            initial_num_runs=3,
            dry_run=True
        )

        # Verify no external processes were called for benchmarking
        mock_subprocess.assert_not_called()
        mock_popen.assert_not_called()

        # Verify we got a valid plan
        assert optimal_config.benchmark_plan is not None


def test_dry_run_with_no_gpus_system():
    """Test dry-run functionality with a system that has no GPUs."""
    no_gpu_system = SystemProfile(
        cpu_cores=4,
        total_ram_gb=8.0,
        gpus=[],
        numa_detected=False,
        blas_backend="OpenBLAS"
    )

    mock_model = ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=16,
        quantization_type="Q8_0"
    )

    engine = BenchmarkingEngine(use_cache=False)

    with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=no_gpu_system), \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model):

        # Run benchmark in dry-run mode
        optimal_config, all_results = engine.run_benchmark(
            model_path="/path/to/model.gguf",
            ctx_size=1024,
            use_case="default",
            initial_num_runs=3,
            dry_run=True
        )

        # Generate output
        dry_run_output = generate_dry_run_plan(optimal_config.benchmark_plan)

        # Verify no GPU message
        assert "GPUs: None detected (CPU-only mode)" in dry_run_output
        assert "No GPUs detected - will test CPU-only configurations" in dry_run_output

        # Should still have GPU offload phase (testing 0 layers)
        assert "Phase 1: GPU Offload Optimization" in dry_run_output
        assert "Will test up to 16 GPU layers" in dry_run_output
