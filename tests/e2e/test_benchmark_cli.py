import pytest
from typer.testing import C<PERSON><PERSON><PERSON><PERSON>
from llama_tune.cli import app

runner = CliRunner()

def test_benchmark_no_model_argument_exits_with_error():
    """
    Test that invoking `llama-tune --benchmark` without `--model-path`
    exits with a clear error message.
    """
    result = runner.invoke(app, ["--benchmark", "--ctx-size", "2048"])
    assert result.exit_code == 1
    assert "Error: --model-path is required when using --benchmark or --dry-run." in result.stdout

def test_benchmark_no_ctx_size_argument_exits_with_error():
    """
    Test that invoking `llama-tune --benchmark` without `--ctx-size`
    exits with a clear error message.
    """
    result = runner.invoke(app, ["--benchmark", "--model-path", "dummy_model.gguf"])
    assert result.exit_code == 1
    assert "Error: --ctx-size is required when using --benchmark or --dry-run." in result.stdout